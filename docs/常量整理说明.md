# Redis Key 和 Room Key 常量整理说明

## 概述

为了提高代码的可维护性和一致性，我们将所有分散在代码中的 Redis Key 前缀和 SocketIO Room Key 前缀整理成统一的常量类 `RedisKeyConstants`。

## 创建的常量类

### `RedisKeyConstants.java`
**路径**: `src/main/java/com/tourism/chat/constants/RedisKeyConstants.java`

## 常量分类

### 1. Redis Key 前缀

#### 在线状态 (Online)
```java
// 用户在线状态
RedisKeyConstants.Online.userKey(tenantId, userId)
// 生成: online:user:{tenantId}:{userId}

// 微信号在线状态  
RedisKeyConstants.Online.wxKey(tenantId, wxId)
// 生成: online:wx:{tenantId}:{wxId}
```

#### 会话映射 (Session)
```java
// Socket会话映射
RedisKeyConstants.Session.socketKey(socketId)
// 生成: session:socket:{socketId}

// 用户会话映射
RedisKeyConstants.Session.userKey(tenantId, userId)
// 生成: session:user:{tenantId}:{userId}

// 微信会话映射
RedisKeyConstants.Session.wxKey(tenantId, wxId)
// 生成: session:wx:{tenantId}:{wxId}
```

#### 群聊信息 (Group)
```java
// 群基本信息
RedisKeyConstants.Group.infoKey(tenantId, groupId)
// 生成: group:info:{tenantId}:{groupId}

// 群成员列表
RedisKeyConstants.Group.membersKey(tenantId, groupId)
// 生成: group:members:{tenantId}:{groupId}
```

#### 消息相关 (Message)
```java
// 用户离线消息
RedisKeyConstants.Message.offlineUserKey(tenantId, userId)
// 生成: msg:offline:{tenantId}:{userId}

// 微信离线消息
RedisKeyConstants.Message.offlineWxKey(tenantId, wxId)
// 生成: msg:offline:wx:{tenantId}:{wxId}
```

### 2. Redis Pub/Sub 频道 (Channel)
```java
RedisKeyConstants.Channel.GROUP_MESSAGES     // "socket:group:messages"
RedisKeyConstants.Channel.PRIVATE_MESSAGES   // "socket:private:messages"
RedisKeyConstants.Channel.SYSTEM_MESSAGES    // "socket:system:messages"
RedisKeyConstants.Channel.SOCKET_MESSAGES    // "channel:socket:messages"
RedisKeyConstants.Channel.SOCKET_SYSTEM      // "channel:socket:system"
```

### 3. SocketIO Room 前缀 (Room)
```java
// 用户房间
RedisKeyConstants.Room.userRoom(tenantId, userId)
// 生成: user:{tenantId}:{userId}

// 群聊房间
RedisKeyConstants.Room.groupRoom(tenantId, groupId)
// 生成: group:{tenantId}:{groupId}

// 租户房间
RedisKeyConstants.Room.tenantRoom(tenantId)
// 生成: tenant:{tenantId}

// 微信房间
RedisKeyConstants.Room.wxRoom(tenantId, wxId)
// 生成: wx:{tenantId}:{wxId}
```

### 4. 系统消息类型 (SystemMessageType)
```java
RedisKeyConstants.SystemMessageType.USER_ONLINE        // "USER_ONLINE"
RedisKeyConstants.SystemMessageType.USER_OFFLINE       // "USER_OFFLINE"
RedisKeyConstants.SystemMessageType.FORCE_DISCONNECT   // "FORCE_DISCONNECT"
RedisKeyConstants.SystemMessageType.BROADCAST          // "BROADCAST"
RedisKeyConstants.SystemMessageType.CUSTOM_EVENT       // "CUSTOM_EVENT"
```

### 5. SocketIO 事件名称 (Event)
```java
RedisKeyConstants.Event.GROUP_MESSAGE       // "group_message"
RedisKeyConstants.Event.PRIVATE_MESSAGE     // "private_message"
RedisKeyConstants.Event.USER_ONLINE         // "user_online"
RedisKeyConstants.Event.USER_OFFLINE        // "user_offline"
RedisKeyConstants.Event.FORCE_DISCONNECT    // "force_disconnect"
RedisKeyConstants.Event.SYSTEM_BROADCAST    // "system_broadcast"
RedisKeyConstants.Event.GLOBAL_BROADCAST    // "global_broadcast"
```

### 6. WebSocket 会话键 (WebSocketSession)
```java
RedisKeyConstants.WebSocketSession.CHATROOM_SEPARATOR  // "|"
```

## 更新的文件列表

### 核心类更新
1. **MessagePublisher.java** - 使用频道和消息类型常量
2. **GroupMessageSubscriber.java** - 使用房间和事件常量
3. **PrivateMessageSubscriber.java** - 使用房间和事件常量
4. **SystemMessageSubscriber.java** - 使用消息类型、房间和事件常量
5. **RedisConfig.java** - 使用频道常量
6. **SocketIoServerRunner.java** - 使用在线状态、房间和事件常量

### 测试类更新
7. **MessageSubscriberTest.java** - 使用频道和消息类型常量

## 使用示例

### 替换前
```java
// 旧代码
String room = "group:" + tenantId + ":" + groupId;
redisTemplate.convertAndSend("socket:group:messages", message);
client.joinRoom("user:" + tenantId + ":" + userId);
redisTemplate.opsForValue().set("online:" + tenantId + ":" + userId, sessionId);
```

### 替换后
```java
// 新代码
String room = RedisKeyConstants.Room.groupRoom(tenantId, groupId);
redisTemplate.convertAndSend(RedisKeyConstants.Channel.GROUP_MESSAGES, message);
client.joinRoom(RedisKeyConstants.Room.userRoom(tenantId, userId));
redisTemplate.opsForValue().set(RedisKeyConstants.Online.userKey(tenantId, userId), sessionId);
```

## 优势

### 1. 类型安全
- 编译时检查，避免字符串拼写错误
- IDE 自动补全和重构支持

### 2. 统一管理
- 所有键名规范集中在一个地方
- 便于修改和维护

### 3. 可读性提升
- 代码意图更加清晰
- 减少魔法字符串

### 4. 扩展性好
- 新增键类型只需在常量类中添加
- 保持命名规范的一致性

## 最佳实践

### 1. 使用方法
- 优先使用提供的静态方法构建键名
- 避免直接使用前缀常量进行字符串拼接

### 2. 命名规范
- 所有常量使用大写字母和下划线
- 方法名使用驼峰命名法
- 保持语义清晰

### 3. 扩展指南
- 新增键类型时，在对应的内部类中添加
- 提供静态方法用于键名构建
- 添加详细的注释说明

## 注意事项

1. **向后兼容**: 所有常量值保持与原有硬编码字符串一致
2. **性能影响**: 静态方法调用对性能影响微乎其微
3. **团队协作**: 新团队成员需要了解常量类的使用方式
4. **代码审查**: 确保新代码使用常量而不是硬编码字符串

这次常量整理大大提升了代码的可维护性和一致性，为后续的功能扩展奠定了良好的基础。

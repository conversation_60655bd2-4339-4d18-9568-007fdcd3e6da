package com.tourism.chat.socket;

import com.alibaba.fastjson2.JSON;
import com.tourism.chat.constants.RedisKeyConstants;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

/**
 * Redis消息订阅者测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class MessageSubscriberTest {

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    public void testGroupMessagePublish() throws InterruptedException {
        // 创建群聊消息
        ChatMessage groupMessage = new ChatMessage();
        groupMessage.setTenantId(1L);
        groupMessage.setGroupId("test_group_123");
        groupMessage.setFromUserId("user_001");
        groupMessage.setContent("这是一条测试群聊消息");

        // 发布消息
        messagePublisher.publishGroupMessage(groupMessage);

        // 等待消息处理
        TimeUnit.SECONDS.sleep(1);
        
        System.out.println("群聊消息发布测试完成");
    }

    @Test
    public void testPrivateMessagePublish() throws InterruptedException {
        // 创建私聊消息
        ChatMessage privateMessage = new ChatMessage();
        privateMessage.setTenantId(1L);
        privateMessage.setFromUserId("user_001");
        privateMessage.setToUserId("user_002");
        privateMessage.setContent("这是一条测试私聊消息");

        // 发布消息
        messagePublisher.publishPrivateMessage(privateMessage);

        // 等待消息处理
        TimeUnit.SECONDS.sleep(1);
        
        System.out.println("私聊消息发布测试完成");
    }

    @Test
    public void testSystemMessagePublish() throws InterruptedException {
        // 测试用户上线通知
        messagePublisher.publishUserOnline(1L, "test_user_001");
        TimeUnit.MILLISECONDS.sleep(500);

        // 测试用户下线通知
        messagePublisher.publishUserOffline(1L, "test_user_001");
        TimeUnit.MILLISECONDS.sleep(500);

        // 测试强制断开连接
        messagePublisher.publishForceDisconnect(1L, "test_user_002", "测试强制下线");
        TimeUnit.MILLISECONDS.sleep(500);

        // 测试租户广播
        messagePublisher.publishTenantBroadcast(1L, "租户系统维护通知", "预计维护时间：2小时");
        TimeUnit.MILLISECONDS.sleep(500);

        // 测试全局广播
        messagePublisher.publishGlobalBroadcast("全局系统升级通知", "升级时间：今晚22:00-24:00");
        TimeUnit.MILLISECONDS.sleep(500);

        System.out.println("系统消息发布测试完成");
    }

    @Test
    public void testCustomSystemMessage() throws InterruptedException {
        // 创建自定义系统消息
        SystemMessageSubscriber.SystemMessage customMessage = new SystemMessageSubscriber.SystemMessage();
        customMessage.setType(RedisKeyConstants.SystemMessageType.CUSTOM_EVENT);
        customMessage.setTenantId(1L);
        customMessage.setUserId("test_user_003");
        customMessage.setContent("自定义事件触发");
        customMessage.setTimestamp(System.currentTimeMillis());
        customMessage.setData("{ \"eventType\": \"user_achievement\", \"level\": 5 }");

        // 发布自定义消息
        messagePublisher.publishSystemMessage(customMessage);

        // 等待消息处理
        TimeUnit.SECONDS.sleep(1);
        
        System.out.println("自定义系统消息发布测试完成");
    }

    @Test
    public void testDirectRedisPublish() throws InterruptedException {
        // 直接通过Redis发布消息测试
        ChatMessage testMessage = new ChatMessage();
        testMessage.setTenantId(999L);
        testMessage.setGroupId("direct_test_group");
        testMessage.setFromUserId("direct_user");
        testMessage.setContent("直接Redis发布测试");

        String jsonMessage = JSON.toJSONString(testMessage);
        redisTemplate.convertAndSend(RedisKeyConstants.Channel.GROUP_MESSAGES, jsonMessage);

        // 等待消息处理
        TimeUnit.SECONDS.sleep(1);
        
        System.out.println("直接Redis发布测试完成");
    }

    @Test
    public void testMessageValidation() throws InterruptedException {
        // 测试无效群聊消息（缺少groupId）
        ChatMessage invalidGroupMessage = new ChatMessage();
        invalidGroupMessage.setTenantId(1L);
        invalidGroupMessage.setFromUserId("user_001");
        invalidGroupMessage.setContent("无效群聊消息");
        // 故意不设置groupId

        messagePublisher.publishGroupMessage(invalidGroupMessage);
        TimeUnit.MILLISECONDS.sleep(500);

        // 测试无效私聊消息（缺少toUserId）
        ChatMessage invalidPrivateMessage = new ChatMessage();
        invalidPrivateMessage.setTenantId(1L);
        invalidPrivateMessage.setFromUserId("user_001");
        invalidPrivateMessage.setContent("无效私聊消息");
        // 故意不设置toUserId

        messagePublisher.publishPrivateMessage(invalidPrivateMessage);
        TimeUnit.MILLISECONDS.sleep(500);

        System.out.println("消息验证测试完成");
    }

    @Test
    public void testBatchMessagePublish() throws InterruptedException {
        // 批量发布消息测试
        for (int i = 1; i <= 10; i++) {
            // 群聊消息
            ChatMessage groupMessage = new ChatMessage();
            groupMessage.setTenantId(1L);
            groupMessage.setGroupId("batch_group_" + (i % 3 + 1));
            groupMessage.setFromUserId("batch_user_" + i);
            groupMessage.setContent("批量群聊消息 #" + i);
            messagePublisher.publishGroupMessage(groupMessage);

            // 私聊消息
            ChatMessage privateMessage = new ChatMessage();
            privateMessage.setTenantId(1L);
            privateMessage.setFromUserId("batch_user_" + i);
            privateMessage.setToUserId("batch_target_" + (i % 5 + 1));
            privateMessage.setContent("批量私聊消息 #" + i);
            messagePublisher.publishPrivateMessage(privateMessage);

            // 避免发送过快
            TimeUnit.MILLISECONDS.sleep(100);
        }

        // 等待所有消息处理完成
        TimeUnit.SECONDS.sleep(2);
        
        System.out.println("批量消息发布测试完成");
    }

    @Test
    public void testConcurrentMessagePublish() throws InterruptedException {
        // 并发消息发布测试
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];

        for (int t = 0; t < threadCount; t++) {
            final int threadId = t;
            threads[t] = new Thread(() -> {
                for (int i = 1; i <= 20; i++) {
                    ChatMessage message = new ChatMessage();
                    message.setTenantId(1L);
                    message.setGroupId("concurrent_group_" + threadId);
                    message.setFromUserId("thread_" + threadId + "_user_" + i);
                    message.setContent("并发消息 Thread-" + threadId + " Message-" + i);
                    
                    messagePublisher.publishGroupMessage(message);
                    
                    try {
                        TimeUnit.MILLISECONDS.sleep(50);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 等待消息处理完成
        TimeUnit.SECONDS.sleep(3);
        
        System.out.println("并发消息发布测试完成");
    }
}

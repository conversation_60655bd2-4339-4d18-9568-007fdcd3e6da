package com.tourism.chat.socket;

import com.alibaba.fastjson2.JSON;
import com.corundumstudio.socketio.BroadcastOperations;
import com.corundumstudio.socketio.SocketIOServer;
import io.netty.handler.codec.http.HttpHeaders;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SocketIoServerRunner implements CommandLineRunner {

    private final SocketIOServer server;
    private final StringRedisTemplate redisTemplate;
    private final MessagePublisher messagePublisher;

    public SocketIoServerRunner(SocketIOServer server, StringRedisTemplate redisTemplate, MessagePublisher messagePublisher) {
        this.server = server;
        this.redisTemplate = redisTemplate;
        this.messagePublisher = messagePublisher;
    }

    @Override
    public void run(String... args) {
        // 客户端连接
        server.addConnectListener(client -> {
            HttpHeaders httpHeaders = client.getHandshakeData().getHttpHeaders();
            String token = httpHeaders.get("Authorization");
            if (token == null || !token.startsWith("Bearer ")) {
                client.disconnect();
                return;
            }

            String tenantId = client.getHandshakeData().getSingleUrlParam("tenantId");
            String userId = client.getHandshakeData().getSingleUrlParam("userId");
            String transport = client.getTransport().name(); // WS or POLLING

            if (tenantId == null || userId == null) {
                client.disconnect();
                return;
            }

            // 在线用户写入 Redis
            redisTemplate.opsForValue()
                    .set("online:" + tenantId + ":" + userId, client.getSessionId().toString());

            // 加入房间
            // 1. 租户维度 格式：tenant:{tenantId}
            client.joinRoom("tenant:" + tenantId);
            // 2. 用户维度 格式：user:{tenantId}:{userId}
            client.joinRoom("user:" + tenantId + ":" + userId);
            // 3. 微信维度 格式：wx:{tenantId}:{wxId}
            client.joinRoom("wx:" + tenantId + ":" + userId);
            // 4. 群聊维度 格式：group:{tenantId}:{groupId}
            client.joinRoom("group:" + tenantId + ":" + userId);

            // 发布用户上线通知
            messagePublisher.publishUserOnline(Long.valueOf(tenantId), userId);

            log.info("✅ 用户连接 tenant={} user={} sid={} transport={}%n", tenantId, userId, client.getSessionId(), transport);
        });

        // 客户端断开
        server.addDisconnectListener(client -> {
            String tenantId = client.getHandshakeData().getSingleUrlParam("tenantId");
            String userId = client.getHandshakeData().getSingleUrlParam("userId");
            if (tenantId != null && userId != null) {
                redisTemplate.delete("online:" + tenantId + ":" + userId);

                // 发布用户下线通知
                messagePublisher.publishUserOffline(Long.valueOf(tenantId), userId);
            }
            log.info("❌ 用户断开 tenant={} user={}%n", tenantId, userId);
        });

        // 群聊消息
        server.addEventListener("group_message", ChatMessage.class, (client, data, ackSender) -> {
            String room = "group:" + data.getTenantId() + ":" + data.getGroupId();
            BroadcastOperations roomOperations = server.getRoomOperations(room);
            roomOperations.sendEvent("group_message", data);
            // 使用消息发布器发布到 Redis，让其他实例也能收到
            messagePublisher.publishGroupMessage(data);
            log.info("📩 群聊消息: {}", data);
        });

        // 私聊消息
        server.addEventListener("private_message", ChatMessage.class, (client, data, ackSender) -> {
            String room = "user:" + data.getTenantId() + ":" + data.getToUserId();
            server.getRoomOperations(room).sendEvent("private_message", data);
            // 使用消息发布器发布到 Redis
            messagePublisher.publishPrivateMessage(data);
            log.info("📩 私聊消息: {}", data);
        });

        server.start();
        System.out.println("🚀 Socket.IO 服务已启动 ws://localhost:19092");
        Runtime.getRuntime().addShutdownHook(new Thread(server::stop));
    }


}

package com.tourism.chat.socket;

import com.alibaba.fastjson2.JSON;
import com.corundumstudio.socketio.SocketIOServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

/**
 * 系统消息Redis订阅者
 * 专门处理系统通知消息（如用户上线/下线、踢人、强制下线等）
 */
@Component
@Slf4j
public class SystemMessageSubscriber implements MessageListener {

    private final SocketIOServer server;

    public SystemMessageSubscriber(SocketIOServer server) {
        this.server = server;
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String body = new String(message.getBody());
            String channel = new String(pattern);
            
            log.debug("收到系统消息频道: {}, 内容: {}", channel, body);
            
            // 解析系统消息
            SystemMessage systemMsg = JSON.parseObject(body, SystemMessage.class);
            
            // 根据消息类型处理
            switch (systemMsg.getType()) {
                case "USER_ONLINE":
                    handleUserOnline(systemMsg);
                    break;
                case "USER_OFFLINE":
                    handleUserOffline(systemMsg);
                    break;
                case "FORCE_DISCONNECT":
                    handleForceDisconnect(systemMsg);
                    break;
                case "BROADCAST":
                    handleBroadcast(systemMsg);
                    break;
                default:
                    log.warn("未知的系统消息类型: {}", systemMsg.getType());
            }
            
        } catch (Exception e) {
            log.error("处理系统消息时发生错误", e);
        }
    }

    /**
     * 处理用户上线通知
     */
    private void handleUserOnline(SystemMessage systemMsg) {
        String room = "tenant:" + systemMsg.getTenantId();
        server.getRoomOperations(room).sendEvent("user_online", systemMsg);
        log.info("✅ 用户上线通知已发送: 租户={}, 用户={}", 
                systemMsg.getTenantId(), systemMsg.getUserId());
    }

    /**
     * 处理用户下线通知
     */
    private void handleUserOffline(SystemMessage systemMsg) {
        String room = "tenant:" + systemMsg.getTenantId();
        server.getRoomOperations(room).sendEvent("user_offline", systemMsg);
        log.info("✅ 用户下线通知已发送: 租户={}, 用户={}", 
                systemMsg.getTenantId(), systemMsg.getUserId());
    }

    /**
     * 处理强制断开连接
     */
    private void handleForceDisconnect(SystemMessage systemMsg) {
        String userRoom = "user:" + systemMsg.getTenantId() + ":" + systemMsg.getUserId();
        
        // 向指定用户发送强制下线通知
        server.getRoomOperations(userRoom).sendEvent("force_disconnect", systemMsg);
        
        // 断开该用户的所有连接
        server.getRoomOperations(userRoom).getClients().forEach(client -> {
            log.info("强制断开用户连接: sessionId={}, 用户={}", 
                    client.getSessionId(), systemMsg.getUserId());
            client.disconnect();
        });
        
        log.info("✅ 强制断开连接处理完成: 租户={}, 用户={}", 
                systemMsg.getTenantId(), systemMsg.getUserId());
    }

    /**
     * 处理广播消息
     */
    private void handleBroadcast(SystemMessage systemMsg) {
        if (systemMsg.getTenantId() != null) {
            // 租户级广播
            String room = "tenant:" + systemMsg.getTenantId();
            server.getRoomOperations(room).sendEvent("system_broadcast", systemMsg);
            log.info("✅ 租户广播消息已发送: 租户={}", systemMsg.getTenantId());
        } else {
            // 全局广播
            server.getBroadcastOperations().sendEvent("global_broadcast", systemMsg);
            log.info("✅ 全局广播消息已发送");
        }
    }

    /**
     * 系统消息实体类
     */
    public static class SystemMessage {
        private String type;        // 消息类型
        private Long tenantId;      // 租户ID
        private String userId;      // 用户ID
        private String content;     // 消息内容
        private Object data;        // 附加数据
        private Long timestamp;     // 时间戳

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public Long getTenantId() { return tenantId; }
        public void setTenantId(Long tenantId) { this.tenantId = tenantId; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        
        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }

        @Override
        public String toString() {
            return "SystemMessage{" +
                    "type='" + type + '\'' +
                    ", tenantId=" + tenantId +
                    ", userId='" + userId + '\'' +
                    ", content='" + content + '\'' +
                    ", timestamp=" + timestamp +
                    '}';
        }
    }
}

package com.tourism.chat.socket;

import com.alibaba.fastjson2.JSON;
import com.corundumstudio.socketio.SocketIOServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RedisMessageSubscriber implements MessageListener {

    private final SocketIOServer server;

    public RedisMessageSubscriber(SocketIOServer server) {
        this.server = server;
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String body = new String(message.getBody());
        // fastjson
        ChatMessage chat = JSON.parseObject(body, ChatMessage.class);

        // 转发给对应房间
        if (chat.getGroupId() != null) {
            String room = "group:" + chat.getTenantId() + ":" + chat.getGroupId();
            server.getRoomOperations(room).sendEvent("group_message", chat);
        } else if (chat.getToUserId() != null) {
            String room = "user:" + chat.getTenantId() + ":" + chat.getToUserId();
            server.getRoomOperations(room).sendEvent("private_message", chat);
        }
        log.info("转发消息: {}", chat);
    }
}

package com.tourism.chat.socket;

import org.springframework.context.annotation.Configuration;

@Configuration
public class RedisConfig {
//    为了支持多租户 + 多微信号 + 分布式部署，建议采用 层级命名 + 前缀分类：
//
//            🔑 在线状态
//
//    online:user:{tenantId}:{userId} → 用户是否在线（存 socketId 或状态）
//
//    online:wx:{tenantId}:{wxId} → 微信号是否在线（一个微信号可能挂多台设备）
//
//            🔑 会话映射
//
//    session:socket:{socketId} → 存 socket 绑定的用户、微信号信息
//
//    session:user:{tenantId}:{userId} → 用户绑定的 socketId 列表
//
//    session:wx:{tenantId}:{wxId} → 微信号绑定的 socketId 列表
//
//🔑 群聊信息
//
//    group:info:{tenantId}:{groupId} → 群基本信息（名称、群主等）
//
//    group:members:{tenantId}:{groupId} → 群成员列表（userId 或 wxId 集合）
//
//            🔑 消息相关
//
//    msg:offline:{tenantId}:{userId} → 存用户离线消息列表
//
//    msg:offline:wx:{tenantId}:{wxId} → 存某个微信号的离线消息
//
//🔑 Pub/Sub 通道
//
//    channel:socket:messages → 跨节点消息广播
//
//    channel:socket:system → 系统通知（踢人、强制下线）

}

package com.tourism.chat.socket;

import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.Transport;
import org.springframework.context.annotation.Bean;

import com.corundumstudio.socketio.Configuration;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.Transport;
import org.springframework.context.annotation.Bean;

@org.springframework.context.annotation.Configuration
public class SocketIoConfig {

    @Bean
    public SocketIOServer socketIOServer() {
        Configuration config = new Configuration();
        config.setHostname("0.0.0.0");
        config.setPort(19092);

        // 支持 WebSocket + 轮询
        config.setTransports(Transport.WEBSOCKET, Transport.POLLING);

        // 心跳 & 超时
        config.setPingInterval(25000);
        config.setPingTimeout(60000);

        return new SocketIOServer(config);
    }
}

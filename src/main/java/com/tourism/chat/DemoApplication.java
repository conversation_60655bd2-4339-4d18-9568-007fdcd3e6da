package com.tourism.chat;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Application entry for Tourism Chat (multi-tenant skeleton)
 */
@MapperScan({"com.tourism.**.mapper", "com.tourism.chat.wx.mappper"})
@EnableAspectJAutoProxy
@EnableAsync
@SpringBootApplication
public class DemoApplication {
    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }
}


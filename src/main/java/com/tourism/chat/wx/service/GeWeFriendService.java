package com.tourism.chat.wx.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.chat.wx.entity.GeWeFriend;

import java.util.List;
import java.util.Optional;

public interface GeWeFriendService extends IService<GeWeFriend> {
    Optional<GeWeFriend> findByWxId(String userName);

    List<GeWeFriend> searchByKeyword(String keyword);

    List<GeWeFriend> findAllGroups();

    List<GeWeFriend> findAllFriends();

    List<GeWeFriend> findByOwnerWxId(String ownerWxId);
}

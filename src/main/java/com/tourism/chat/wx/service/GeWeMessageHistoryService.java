package com.tourism.chat.wx.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.chat.group.entity.GeweGroupMessage;
import com.tourism.chat.wx.entity.GeWeMessage;
import com.tourism.chat.wx.model.WeChatMessage;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * GeWe消息历史服务接口
 */
public interface GeWeMessageHistoryService {

    /**
     * 保存消息
     */
    GeWeMessage saveMessage(GeWeMessage message);

    /**
     * 保存发送的消息
     */
    GeWeMessage saveSentMessage(String fromWxId, String toWxId, String content,
                                Integer messageType, Boolean isGroup, String atWxIds, WeChatMessage message);

    /**
     * 保存接收的消息
     */
    GeWeMessage saveReceivedMessage(String messageId, String fromWxId, String toWxId,
                                   String content, Integer messageType, Boolean isGroup,
                                   String atWxIds, LocalDateTime sendTime);

    /**
     * 保存接收的消息（带扩展数据）
     */
    GeWeMessage saveReceivedMessage(String messageId, String fromWxId, String toWxId,
                                   String content, Integer messageType, Boolean isGroup,
                                   String atWxIds, LocalDateTime sendTime, String extraData);

    /**
     * 获取两个用户之间的聊天记录
     */
    Page<GeWeMessage> getChatHistory(String wxId1, String wxId2, Pageable pageable);

    /**
     * 获取群聊消息记录
     */
    Page<GeweGroupMessage> getGroupChatHistory(String groupWxId, Pageable pageable);

    /**
     * 获取用户的所有消息记录
     */
    Page<GeWeMessage> getUserAllMessages(String wxId, Pageable pageable);

    /**
     * 获取指定时间范围内的消息
     */
    Page<GeWeMessage> getMessagesByTimeRange(LocalDateTime startTime, LocalDateTime endTime, 
                                           Pageable pageable);

    /**
     * 统计聊天消息数量
     */
    long countChatMessages(String wxId1, String wxId2);

    /**
     * 统计群聊消息数量
     */
    long countGroupMessages(String groupWxId);

    /**
     * 获取最近的聊天联系人
     */
    List<String> getRecentChatContacts(String wxId, int limit);

    /**
     * 获取最近的群聊
     */
    List<String> getRecentGroups(String wxId, int limit);

    /**
     * 更新消息状态
     */
    void updateMessageStatus(Long messageId, Integer status);

    /**
     * 根据消息ID查找消息
     */
    GeWeMessage findByMessageId(String messageId);

    /**
     * 删除指定时间之前的消息
     */
    void deleteMessagesBefore(LocalDateTime beforeTime);
}

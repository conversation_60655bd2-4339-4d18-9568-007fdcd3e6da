package com.tourism.chat.wx.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tourism.chat.wx.conf.GeWeApiProperties;
import com.tourism.chat.wx.model.GeWeApiResponse;
import com.tourism.chat.wx.model.GeWeContactsData;
import com.tourism.chat.wx.model.GeWeFriendInfo;
import com.tourism.chat.wx.model.WeChatMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GeWe API客户端服务
 */
@Service
@Slf4j
public class GeWeApiService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private GeWeApiProperties geWeApiProperties;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 获取通讯录列表
     */
    public GeWeContactsData fetchContactsList() {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/contacts/fetchContactsList";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("appId", geWeApiProperties.getAppId());

        HttpHeaders headers = createHeaders();
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.POST, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                // unkonw properties
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                GeWeApiResponse<GeWeContactsData> apiResponse = objectMapper.readValue(
                        response.getBody(),
                        new TypeReference<GeWeApiResponse<GeWeContactsData>>() {
                        });

                if (apiResponse.isSuccess()) {
                    return apiResponse.getData();
                } else {
                    log.error("获取通讯录列表失败: {}", apiResponse.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("调用GeWe API获取通讯录列表异常", e);
        }

        return new GeWeContactsData();
    }

    /**
     * 批量获取好友简要信息
     */
    public List<GeWeFriendInfo> getBriefInfo(List<String> wxids) {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/contacts/getBriefInfo";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("appId", geWeApiProperties.getAppId());
        requestBody.put("wxids", wxids);

        HttpHeaders headers = createHeaders();
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.POST, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                GeWeApiResponse<List<GeWeFriendInfo>> apiResponse = objectMapper.readValue(
                        response.getBody(),
                        new TypeReference<GeWeApiResponse<List<GeWeFriendInfo>>>() {
                        });

                if (apiResponse.isSuccess()) {
                    return apiResponse.getData();
                } else {
                    log.error("获取好友简要信息失败: {}", apiResponse.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("调用GeWe API获取好友简要信息异常", e);
        }

        return List.of();
    }

    /**
     * 发送文字消息
     */
    public WeChatMessage sendTextMessage(String toWxid, String content, String ats) {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/message/postText";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("appId", geWeApiProperties.getAppId());
        requestBody.put("toWxid", toWxid);
        requestBody.put("content", content);
        if (ats != null && !ats.isEmpty()) {
            requestBody.put("ats", ats);
        }

        HttpHeaders headers = createHeaders();
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.POST, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                GeWeApiResponse<WeChatMessage> apiResponse = objectMapper.readValue(
                        response.getBody(),
                        new TypeReference<GeWeApiResponse<WeChatMessage>>() {
                        });

                if (apiResponse.isSuccess()) {
                    log.info("发送消息成功: toWxid={}, content={}", toWxid, content);
                    return apiResponse.getData();
                } else {
                    log.error("发送消息失败: {}", apiResponse.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("调用GeWe API发送消息异常", e);
        }

        return null;
    }

    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-GEWE-TOKEN", geWeApiProperties.getToken());
        headers.set("Content-Type", "application/json");
        return headers;
    }

    // ===== Group(chatroom) related APIs =====
    public com.tourism.chat.wx.model.GeWeChatroomModels.ChatroomInfoData getChatroomInfo(String chatroomId) {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/group/getChatroomInfo";
        Map<String, Object> body = new HashMap<>();
        body.put("appId", geWeApiProperties.getAppId());
        body.put("chatroomId", chatroomId);
        HttpEntity<Map<String, Object>> req = new HttpEntity<>(body, createHeaders());
        try {
            ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.POST, req, String.class);
            if (resp.getStatusCode().is2xxSuccessful()) {
                GeWeApiResponse<com.tourism.chat.wx.model.GeWeChatroomModels.ChatroomInfoData> api = objectMapper
                        .readValue(
                                resp.getBody(),
                                new TypeReference<GeWeApiResponse<com.tourism.chat.wx.model.GeWeChatroomModels.ChatroomInfoData>>() {
                                });
                if (api.isSuccess())
                    return api.getData();
            }
        } catch (Exception e) {
            log.error("getChatroomInfo error", e);
        }
        return null;
    }

    public com.tourism.chat.wx.model.GeWeChatroomModels.MemberListData getChatroomMemberList(String chatroomId) {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/group/getChatroomMemberList";
        Map<String, Object> body = new HashMap<>();
        body.put("appId", geWeApiProperties.getAppId());
        body.put("chatroomId", chatroomId);
        HttpEntity<Map<String, Object>> req = new HttpEntity<>(body, createHeaders());
        try {
            ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.POST, req, String.class);
            if (resp.getStatusCode().is2xxSuccessful()) {
                GeWeApiResponse<com.tourism.chat.wx.model.GeWeChatroomModels.MemberListData> api = objectMapper
                        .readValue(
                                resp.getBody(),
                                new TypeReference<GeWeApiResponse<com.tourism.chat.wx.model.GeWeChatroomModels.MemberListData>>() {
                                });
                if (api.isSuccess())
                    return api.getData();
            }
        } catch (Exception e) {
            log.error("getChatroomMemberList error", e);
        }
        return null;
    }

    public java.util.List<com.tourism.chat.wx.model.GeWeChatroomModels.MemberDetail> getChatroomMemberDetail(
            String chatroomId, java.util.List<String> memberWxids) {
        String url = geWeApiProperties.getBaseUrl() + "/gewe/v2/api/group/getChatroomMemberDetail";
        Map<String, Object> body = new HashMap<>();
        body.put("appId", geWeApiProperties.getAppId());
        body.put("chatroomId", chatroomId);
        body.put("memberWxids", memberWxids);
        HttpEntity<Map<String, Object>> req = new HttpEntity<>(body, createHeaders());
        try {
            ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.POST, req, String.class);
            if (resp.getStatusCode().is2xxSuccessful()) {
                GeWeApiResponse<java.util.List<com.tourism.chat.wx.model.GeWeChatroomModels.MemberDetail>> api = objectMapper
                        .readValue(
                                resp.getBody(),
                                new TypeReference<GeWeApiResponse<java.util.List<com.tourism.chat.wx.model.GeWeChatroomModels.MemberDetail>>>() {
                                });
                if (api.isSuccess())
                    return api.getData();
            }
        } catch (Exception e) {
            log.error("getChatroomMemberDetail error", e);
        }
        return java.util.Collections.emptyList();
    }
}

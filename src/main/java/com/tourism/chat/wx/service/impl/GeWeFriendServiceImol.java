package com.tourism.chat.wx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.chat.wx.entity.GeWeFriend;
import com.tourism.chat.wx.mappper.GeWeFriendMapper;
import com.tourism.chat.wx.service.GeWeFriendService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class GeWeFriendServiceImol extends ServiceImpl<GeWeFriendMapper, GeWeFriend> implements GeWeFriendService {

    @Override
    public Optional<GeWeFriend> findByWxId(String userName) {
        if (userName == null || userName.isEmpty()) {
            return Optional.empty();
        }
        GeWeFriend friend = getOne(new LambdaQueryWrapper<GeWeFriend>()
                .eq(GeWeFriend::getWxId, userName));
        if (friend != null) {
            return Optional.of(friend);
        }
        return Optional.empty();
    }

    @Override
    public List<GeWeFriend> searchByKeyword(String keyword) {
        if (keyword == null || keyword.isEmpty()) {
            return List.of();
        }
        List<GeWeFriend> geWeFriends = this.baseMapper.searchByKeyword(keyword);
        return geWeFriends;
    }

    @Override
    public List<GeWeFriend> findAllGroups() {
        List<GeWeFriend> allGroups = this.baseMapper.findAllGroups();
        if (allGroups != null && !allGroups.isEmpty()) {
            return allGroups;
        }
        return List.of();
    }

    @Override
    public List<GeWeFriend> findAllFriends() {
        List<GeWeFriend> allFriends = this.baseMapper.findAllFriends();
        if (allFriends != null && !allFriends.isEmpty()) {
            return allFriends;
        }
        return List.of();
    }

    @Override
    public List<GeWeFriend> findByOwnerWxId(String ownerWxId) {
        List<GeWeFriend> friends = this.lambdaQuery().eq(GeWeFriend::getMasterWxid, ownerWxId).list();
        if (friends != null && !friends.isEmpty()) {
            return friends;
        }
        return List.of();
    }
}

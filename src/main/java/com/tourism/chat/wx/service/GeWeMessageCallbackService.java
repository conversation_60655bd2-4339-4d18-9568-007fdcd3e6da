package com.tourism.chat.wx.service;


import com.tourism.chat.wx.request.GeWeMessageCallbackRequest;

/**
 * GeWe消息回调处理服务接口
 */
public interface GeWeMessageCallbackService {

    /**
     * 处理接收到的消息回调
     */
    void handleMessageCallback(GeWeMessageCallbackRequest request);

    /**
     * 处理文本消息
     */
    void handleTextMessage(GeWeMessageCallbackRequest request);

    /**
     * 处理图片消息
     */
    void handleImageMessage(GeWeMessageCallbackRequest request);

    /**
     * 处理语音消息
     */
    void handleVoiceMessage(GeWeMessageCallbackRequest request);

    /**
     * 处理视频消息
     */
    void handleVideoMessage(GeWeMessageCallbackRequest request);

    /**
     * 处理文件消息
     */
    void handleFileMessage(GeWeMessageCallbackRequest request);

    /**
     * 处理位置消息
     */
    void handleLocationMessage(GeWeMessageCallbackRequest request);

    /**
     * 处理表情消息
     */
    void handleEmojiMessage(GeWeMessageCallbackRequest request);

    /**
     * 处理链接消息
     */
    void handleLinkMessage(GeWeMessageCallbackRequest request);

    /**
     * 处理系统消息
     */
    void handleSystemMessage(GeWeMessageCallbackRequest request);
}

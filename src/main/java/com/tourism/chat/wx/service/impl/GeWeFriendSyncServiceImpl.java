package com.tourism.chat.wx.service.impl;

import com.tourism.chat.wx.entity.GeWeFriend;
import com.tourism.chat.wx.model.GeWeContactsData;
import com.tourism.chat.wx.model.GeWeFriendInfo;
import com.tourism.chat.wx.service.GeWeApiService;
import com.tourism.chat.wx.service.GeWeFriendService;
import com.tourism.chat.wx.service.GeWeFriendSyncService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * GeWe好友同步服务实现类
 */
@Service
@Slf4j
public class GeWeFriendSyncServiceImpl implements GeWeFriendSyncService {

    @Resource
    private GeWeApiService geWeApiService;

    @Resource
    private GeWeFriendService geWeFriendRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncAllFriends() {
        log.info("开始同步所有好友信息...");

        String  ownerWxId = "wxid_xpih49zdmi5a22";;

        // 获取通讯录列表
        GeWeContactsData contactsData = geWeApiService.fetchContactsList();

        if (contactsData == null || CollectionUtils.isEmpty(contactsData.getFriends())) {
            log.warn("获取通讯录列表为空");
            return 0;
        }

        List<String> wxIds = contactsData.getFriends();
        log.info("获取到 {} 个联系人，准备批量获取详细信息", wxIds.size());

        // 批量获取详细信息并保存
        return syncFriendsByWxIds(wxIds, ownerWxId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncFriendsByWxIds(List<String> wxIds, String ownerWxId) {
        if (CollectionUtils.isEmpty(wxIds)) {
            return 0;
        }

        log.info("开始同步 {} 个好友的详细信息", wxIds.size());

        // 分批处理，避免请求过大
        int batchSize = 50;
        int syncCount = 0;

        for (int i = 0; i < wxIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, wxIds.size());
            List<String> batchWxIds = wxIds.subList(i, endIndex);

            log.info("处理第 {}/{} 批，包含 {} 个好友", (i / batchSize + 1),
                    (wxIds.size() + batchSize - 1) / batchSize, batchWxIds.size());

            // 获取批量好友详细信息
            List<GeWeFriendInfo> friendInfos = geWeApiService.getBriefInfo(batchWxIds);

            if (!CollectionUtils.isEmpty(friendInfos)) {
                // 保存到数据库
                List<GeWeFriend> friendsToSave = new ArrayList<>();

                for (GeWeFriendInfo friendInfo : friendInfos) {

                    GeWeFriend friend = convertToEntity(friendInfo);
                    if (friend != null) {
                        friend.setMasterWxid(ownerWxId);
                        friendsToSave.add(friend);
                    }
                }

                if (!friendsToSave.isEmpty()) {
                    geWeFriendRepository.saveBatch(friendsToSave);
                    syncCount += friendsToSave.size();
                    log.info("本批次成功保存 {} 个好友信息", friendsToSave.size());
                }
            }

            // 避免请求过于频繁
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        log.info("好友同步完成，共同步 {} 个好友", syncCount);
        return syncCount;
    }

    @Override
    public List<GeWeFriend> getAllFriends() {
        return geWeFriendRepository.findAllFriends();
    }

    @Override
    public List<GeWeFriend> getAllGroups() {
        return geWeFriendRepository.findAllGroups();
    }

    @Override
    public List<GeWeFriend> searchFriends(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return getAllFriends();
        }
        return geWeFriendRepository.searchByKeyword(keyword);
    }

    @Override
    public GeWeFriend getFriendByWxId(String wxId) {
        if (!StringUtils.hasText(wxId)) {
            return null;
        }
        return geWeFriendRepository.findByWxId(wxId).orElse(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GeWeFriend refreshFriend(String wxId) {
        if (!StringUtils.hasText(wxId)) {
            return null;
        }

        log.info("刷新好友信息: {}", wxId);

        // 从GeWe API获取最新信息
        List<GeWeFriendInfo> friendInfos = geWeApiService.getBriefInfo(List.of(wxId));

        if (CollectionUtils.isEmpty(friendInfos)) {
            log.warn("无法从GeWe API获取好友信息: {}", wxId);
            return null;
        }

        GeWeFriendInfo friendInfo = friendInfos.get(0);
        GeWeFriend friend = convertToEntity(friendInfo);

        if (friend != null) {
            geWeFriendRepository.save(friend);
            log.info("成功刷新好友信息: {}", wxId);
        }

        return friend;
    }

    @Override
    public List<GeWeFriend> getAllFriendsByOwnerWxId(String ownerWxId) {
        if (StringUtils.hasText(ownerWxId)) {
            return geWeFriendRepository.findByOwnerWxId(ownerWxId);
        }
        return List.of();
    }

    /**
     * 将GeWe API返回的好友信息转换为实体对象
     */
    private GeWeFriend convertToEntity(GeWeFriendInfo friendInfo) {
        if (friendInfo == null || !StringUtils.hasText(friendInfo.getUserName())) {
            return null;
        }

        // 检查是否已存在
        Optional<GeWeFriend> existingFriend = geWeFriendRepository.findByWxId(friendInfo.getUserName());

        GeWeFriend friend = existingFriend.orElse(new GeWeFriend());

        // 设置基本信息
        friend.setWxId(friendInfo.getUserName());
        friend.setNickName(friendInfo.getNickName());
        friend.setPyInitial(friendInfo.getPyInitial());
        friend.setQuanPin(friendInfo.getQuanPin());
        friend.setSex(friendInfo.getSex());
        friend.setRemark(friendInfo.getRemark());
        friend.setRemarkPyInitial(friendInfo.getRemarkPyInitial());
        friend.setRemarkQuanPin(friendInfo.getRemarkQuanPin());
        friend.setSignature(friendInfo.getSignature());
        friend.setAlias(friendInfo.getAlias());
        friend.setSnsBgImg(friendInfo.getSnsBgImg());
        friend.setCountry(friendInfo.getCountry());
        friend.setProvince(friendInfo.getProvince());
        friend.setCity(friendInfo.getCity());
        friend.setBigHeadImgUrl(friendInfo.getBigHeadImgUrl());
        friend.setSmallHeadImgUrl(friendInfo.getSmallHeadImgUrl());

        // 判断是否为群聊（微信ID以@chatroom结尾）
        boolean isGroup = friendInfo.getUserName().endsWith("@chatroom");
        friend.setIsGroup(isGroup);

        // 设置时间戳
        LocalDateTime now = LocalDateTime.now();
        if (friend.getId() == null) {
            friend.setCreateTime(now);
        }
        friend.setUpdateTime(now);
        friend.setSynced(true);

        return friend;
    }
}
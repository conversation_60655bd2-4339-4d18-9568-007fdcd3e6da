package com.tourism.chat.wx.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.chat.wx.entity.GeWeMessage;
import com.tourism.chat.wx.model.WeChatMessage;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * GeWe消息发送服务接口
 */
public interface GeWeMessageService extends IService<GeWeMessage> {

    /**
     * 发送文字消息给好友
     *
     * @param wxId    好友微信ID
     * @param content 消息内容
     * @return 是否发送成功
     */
    WeChatMessage sendTextToFriend(String wxId, String content);

    /**
     * 发送文字消息给群聊
     * @param groupWxId 群聊微信ID
     * @param content 消息内容
     * @return 是否发送成功
     */
    WeChatMessage sendTextToGroup(String groupWxId, String content);

    /**
     * 发送文字消息给群聊并@某人
     * @param groupWxId 群聊微信ID
     * @param content 消息内容
     * @param atWxId 要@的人的微信ID
     * @return 是否发送成功
     */
    WeChatMessage sendTextToGroupWithAt(String groupWxId, String content, String atWxId);

    /**
     * 发送文字消息给群聊并@多人
     *
     * @param groupWxId 群聊微信ID
     * @param content   消息内容
     * @param atWxIds   要@的人的微信ID列表，用逗号分隔
     * @return 是否发送成功
     */
    WeChatMessage sendTextToGroupWithMultipleAt(String groupWxId, String content, String atWxIds);

    Optional<GeWeMessage> findByMessageId(String messageId);

    Page<GeWeMessage> findChatHistory(String wxId1, String wxId2, Pageable pageable);

    Page<GeWeMessage> findGroupChatHistory(String groupWxId, Pageable pageable);

    Page<GeWeMessage> findUserAllMessages(String wxId, Pageable pageable);

    Page<GeWeMessage> findMessagesByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    long countChatMessages(String wxId1, String wxId2);

    long countGroupMessages(String groupWxId);

    List<String> findRecentChatContacts(String wxId, Pageable pageable);

    List<String> findRecentGroups(String wxId, Pageable pageable);

    Optional<GeWeMessage> findById(Long messageId);

    void deleteMessagesBefore(LocalDateTime beforeTime);
}
package com.tourism.chat.wx.conf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * GeWe API配置类
 */
@Configuration
@Slf4j
public class GeWeApiConfiguration {

    @Bean
    public RestTemplate restTemplate() {
        log.info("初始化GeWe API RestTemplate");
        return new RestTemplate();
    }
}
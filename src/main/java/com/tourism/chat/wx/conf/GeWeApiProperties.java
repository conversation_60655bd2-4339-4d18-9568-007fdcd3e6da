package com.tourism.chat.wx.conf;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * GeWe API配置属性
 */
@Component
@ConfigurationProperties(prefix = "gewe.api")
@Getter
@Setter
public class GeWeApiProperties {
    
    /**
     * GeWe API基础URL
     */
    private String baseUrl = "http://api.geweapi.com";
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * API Token
     */
    private String token;
    
    /**
     * 请求超时时间（毫秒）
     */
    private int timeout = 30000;
}
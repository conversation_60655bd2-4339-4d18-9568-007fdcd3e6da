package com.tourism.chat.wx.model;

import java.util.List;
import lombok.Data;

/** Models for GeWe group(chatroom) APIs */
public class GeWeChatroomModels {
    @Data
    public static class ChatroomInfoData {
        private String chatroomId;
        private String nickName;
        private String pyInitial;
        private String quanPin;
        private Integer sex;
        private String remark;
        private String remarkPyInitial;
        private String remarkQuanPin;
        private Integer chatRoomNotify;
        private String chatRoomOwner;
        private String smallHeadImgUrl;
        private List<Member> memberList;
    }

    @Data
    public static class Member {
        private String wxid;
        private String nickName;
        private String inviterUserName;
        private Integer memberFlag;
        private String displayName;
        private String bigHeadImgUrl;
        private String smallHeadImgUrl;
    }

    @Data
    public static class MemberListData {
        private List<Member> memberList;
        private String chatroomOwner;
        private List<String> adminWxid;
    }

    @Data
    public static class MemberDetail {
        private String userName;
        private String nickName;
        private String pyInitial;
        private String quanPin;
        private Integer sex;
        private String remark;
        private Integer chatRoomNotify;
        private String signature;
        private String alias;
        private String snsBgImg;
        private String bigHeadImgUrl;
        private String smallHeadImgUrl;
        private String country;
        private String province;
        private String city;
        private String friendUserName;
        private String inviterUserName;
        private Integer memberFlag;
    }
}


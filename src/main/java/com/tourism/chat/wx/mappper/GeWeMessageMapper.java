package com.tourism.chat.wx.mappper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.chat.wx.entity.GeWeMessage;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * GeWe消息仓库接口
 */
@Repository
public interface GeWeMessageMapper extends BaseMapper<GeWeMessage> {

       /**
        * 根据消息ID查找消息
        */
       @Select("SELECT * FROM gewe_message WHERE message_id = #{messageId} ")
       GeWeMessage findByMessageId(@Param("messageId") String messageId, @Param("tenantId") Long tenantId);

       /**
        * 查找两个用户之间的聊天记录（分页）
        */
       @Select("SELECT * FROM gewe_message WHERE " +
                     "((from_wx_id = #{wxId1} AND to_wx_id = #{wxId2}) OR " +
                     " (from_wx_id = #{wxId2} AND to_wx_id = #{wxId1})) AND " +
                     "is_group = false  " +
                     "ORDER BY send_time ASC")
       Page<GeWeMessage> findChatHistory(@Param("wxId1") String wxId1,
                     @Param("wxId2") String wxId2,

                     Page<GeWeMessage> page);

       /**
        * 查找群聊消息记录（分页）
        */
       @Select("SELECT * FROM gewe_message WHERE " +
                     "to_wx_id = #{groupWxId} AND is_group = true  " +
                     "ORDER BY send_time DESC")
       Page<GeWeMessage> findGroupChatHistory(@Param("groupWxId") String groupWxId,

                     Page<GeWeMessage> page);

       /**
        * 查找用户的所有聊天记录（分页）
        */
       @Select("SELECT * FROM gewe_message WHERE " +
                     "(from_wx_id = #{wxId} OR (to_wx_id = #{wxId} AND is_group = false))  " +
                     "ORDER BY send_time DESC")
       Page<GeWeMessage> findUserAllMessages(@Param("wxId") String wxId,

                     Page<GeWeMessage> page);

       /**
        * 查找指定时间范围内的消息
        */
       @Select("SELECT * FROM gewe_message WHERE " +
                     "send_time BETWEEN #{startTime} AND #{endTime}  " +
                     "ORDER BY send_time DESC")
       Page<GeWeMessage> findMessagesByTimeRange(@Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime,

                     Page<GeWeMessage> page);

       /**
        * 统计两个用户之间的消息数量
        */
       @Select("SELECT COUNT(*) FROM gewe_message WHERE " +
                     "((from_wx_id = #{wxId1} AND to_wx_id = #{wxId2}) OR " +
                     " (from_wx_id = #{wxId2} AND to_wx_id = #{wxId1})) AND " +
                     "is_group = false ")
       long countChatMessages(@Param("wxId1") String wxId1, @Param("wxId2") String wxId2,
                     @Param("tenantId") Long tenantId);

       /**
        * 统计群聊消息数量
        */
       @Select("SELECT COUNT(*) FROM gewe_message WHERE " +
                     "to_wx_id = #{groupWxId} AND is_group = true ")
       long countGroupMessages(@Param("groupWxId") String groupWxId, @Param("tenantId") Long tenantId);

       /**
        * 查找最近的聊天联系人
        */
       @Select("<script>" +
                     "SELECT DISTINCT CASE " +
                     "WHEN from_wx_id = #{wxId} THEN to_wx_id " +
                     "ELSE from_wx_id END as contact_wx_id " +
                     "FROM gewe_message WHERE " +
                     "(from_wx_id = #{wxId} OR to_wx_id = #{wxId}) AND is_group = false  " +
                     "GROUP BY contact_wx_id " +
                     "ORDER BY MAX(send_time) DESC " +

                     "</script>")
       List<String> findRecentChatContacts(@Param("wxId") String wxId, @Param("page") Page<String> page);

       /**
        * 查找最近的群聊
        */
       @Select("<script>" +
                     "SELECT DISTINCT to_wx_id FROM gewe_message WHERE " +
                     "from_wx_id = #{wxId} AND is_group = true  " +
                     "GROUP BY to_wx_id " +
                     "ORDER BY MAX(send_time) DESC " +
                     "</script>")
       List<String> findRecentGroups(@Param("wxId") String wxId, @Param("page") Page<String> page);

       /**
        * 删除指定时间之前的消息
        */
       @Delete("DELETE FROM gewe_message WHERE send_time < #{beforeTime} ")
       void deleteMessagesBefore(@Param("beforeTime") LocalDateTime beforeTime, @Param("tenantId") Long tenantId);

       /** 最近一条好友消息（双向） */
       @Select("SELECT * FROM gewe_message WHERE ((from_wx_id = #{wxId1} AND to_wx_id = #{wxId2}) OR (from_wx_id = #{wxId2} AND to_wx_id = #{wxId1})) AND is_group = false and message_type = 1  ORDER BY send_time DESC " +
               "LIMIT 1")
       GeWeMessage findLastFriendMessage(@Param("wxId1") String wxId1, @Param("wxId2") String wxId2);

       /** 最近一条群聊消息 */
       @Select("SELECT * FROM gewe_message WHERE to_wx_id = #{groupWxId} AND is_group = true ORDER BY send_time DESC LIMIT 1")
       GeWeMessage findLastGroupMessage(@Param("groupWxId") String groupWxId);

       /** 好友未读数 */
       @Select("SELECT COUNT(*) FROM gewe_message WHERE from_wx_id = #{contactWxId} AND to_wx_id = #{wxId} AND is_group = false AND direction = 2 AND (status IS NULL OR status <> 5)")
       int countUnreadFriend(@Param("wxId") String wxId, @Param("contactWxId") String contactWxId);

       /** 群聊未读数（简化） */
       @Select("SELECT COUNT(*) FROM gewe_message WHERE to_wx_id = #{groupWxId} AND is_group = true AND direction = 2 AND (status IS NULL OR status <> 5)")
       int countUnreadGroup(@Param("groupWxId") String groupWxId);

       /** 标记好友会话消息为已读 */
       @Update("UPDATE gewe_message SET status = 5 WHERE from_wx_id = #{contactWxId} AND to_wx_id = #{wxId} AND is_group = false AND direction = 2 AND (status IS NULL OR status <> 5)")
       int markFriendConversationRead(@Param("wxId") String wxId, @Param("contactWxId") String contactWxId);

       /** 标记群聊消息为已读 */
       @Update("UPDATE gewe_message SET status = 5 WHERE to_wx_id = #{groupWxId} AND is_group = true AND direction = 2 AND (status IS NULL OR status <> 5)")
       int markGroupConversationRead(@Param("groupWxId") String groupWxId);
}

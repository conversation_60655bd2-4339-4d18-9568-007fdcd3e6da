package com.tourism.chat.wx.mappper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.chat.wx.entity.GeWeFriend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * GeWe好友Repository
 */
@Mapper
public interface GeWeFriendMapper extends BaseMapper<GeWeFriend> {

    /**
     * 根据微信ID查找好友
     */
    @Select("SELECT * FROM gewe_friend WHERE wx_id = #{wxId} ")
    GeWeFriend findByWxId(@Param("wxId") String wxId);

    /**
     * 根据微信ID列表查找好友
     */
    @Select("<script>" +
            "SELECT * FROM gewe_friend WHERE wx_id IN " +
            "<foreach item='item' index='index' collection='wxIds' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            " " +
            "</script>")
    List<GeWeFriend> findByWxIdIn(@Param("wxIds") List<String> wxIds);

    /**
     * 查找所有好友（排除群聊）
     */
    @Select("SELECT * FROM gewe_friend WHERE is_group = false  ORDER BY remark_py_initial, py_initial")
    List<GeWeFriend> findAllFriends();

    /**
     * 查找所有群聊
     */
    @Select("SELECT * FROM gewe_friend WHERE is_group = true  ORDER BY remark_py_initial, py_initial")
    List<GeWeFriend> findAllGroups();

    /**
     * 根据昵称或备注模糊搜索
     */
    @Select("SELECT * FROM gewe_friend WHERE (nick_name LIKE CONCAT('%', #{keyword}, '%') OR remark LIKE CONCAT('%', #{keyword}, '%'))  ORDER BY remark_py_initial, py_initial")
    List<GeWeFriend> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 检查微信ID是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM gewe_friend WHERE wx_id = #{wxId} ")
    boolean existsByWxId(@Param("wxId") String wxId);
}
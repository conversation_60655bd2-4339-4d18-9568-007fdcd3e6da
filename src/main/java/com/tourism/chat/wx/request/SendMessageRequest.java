package com.tourism.chat.wx.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 发送消息请求DTO
 */
@Getter
@Setter
public class SendMessageRequest {
    
    /**
     * 接收者微信ID
     */
    @NotBlank(message = "接收者微信ID不能为空")
    private String toWxId;

    private String fromWxid;
    
    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String content;
    
    /**
     * @的用户微信ID（群聊时使用，多个用逗号分隔）
     */
    private String atWxIds;
    
    /**
     * 消息类型（friend: 好友消息, group: 群聊消息）
     */
    private String messageType = "friend";
}
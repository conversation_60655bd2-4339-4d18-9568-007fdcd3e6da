package com.tourism.chat.wx.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * GeWe消息回调请求模型
 * 根据 GeWe API 实际回调格式定义
 */
@Getter
@Setter
@ToString
public class GeWeMessageCallbackRequest {

    /**
     * 消息类型名称
     */
    @JsonProperty("TypeName")
    private String typeName;

    /**
     * 应用ID
     */
    @JsonProperty("Appid")
    private String appid;

    /**
     * 微信ID
     */
    @JsonProperty("Wxid")
    private String wxid;

    /**
     * 消息数据
     */
    @JsonProperty("Data")
    private MessageData data;

    private Long tenantId;

    /**
     * 消息数据内部类
     */
    @Getter
    @Setter
    @ToString
    public static class MessageData {

        /**
         * 消息ID
         */
        @JsonProperty("MsgId")
        private Long msgId;

        /**
         * 发送者信息
         */
        @JsonProperty("FromUserName")
        private StringWrapper fromUserName;

        /**
         * 接收者信息
         */
        @JsonProperty("ToUserName")
        private StringWrapper toUserName;

        /**
         * 消息类型
         * 1: 文本消息
         * 3: 图片消息
         * 34: 语音消息
         * 43: 视频消息
         * 49: 文件消息/链接消息/小程序等
         * 48: 位置消息
         * 47: 表情消息
         * 10000: 系统消息
         */
        @JsonProperty("MsgType")
        private Integer msgType;

        /**
         * 消息内容
         */
        @JsonProperty("Content")
        private StringWrapper content;

        /**
         * 消息状态
         */
        @JsonProperty("Status")
        private Integer status;

        /**
         * 图片状态
         */
        @JsonProperty("ImgStatus")
        private Integer imgStatus;

        /**
         * 图片缓冲区
         */
        @JsonProperty("ImgBuf")
        private ImgBuf imgBuf;

        /**
         * 创建时间（时间戳）
         */
        @JsonProperty("CreateTime")
        private Long createTime;

        /**
         * 消息源信息
         */
        @JsonProperty("MsgSource")
        private String msgSource;

        /**
         * 推送内容
         */
        @JsonProperty("PushContent")
        private String pushContent;

        /**
         * 新消息ID
         */
        @JsonProperty("NewMsgId")
        private Long newMsgId;

        /**
         * 消息序号
         */
        @JsonProperty("MsgSeq")
        private Long msgSeq;
    }

    /**
     * 字符串包装类
     */
    @Getter
    @Setter
    public static class StringWrapper {
        @JsonProperty("string")
        private String string;
    }

    /**
     * 图片缓冲区类
     */
    @Getter
    @Setter
    public static class ImgBuf {
        @JsonProperty("iLen")
        private Integer iLen;
    }

    // 便捷方法
    public String getFromWxId() {
        return data != null && data.fromUserName != null ? data.fromUserName.string : null;
    }

    public String getToWxId() {
        return data != null && data.toUserName != null ? data.toUserName.string : null;
    }

    public String getContent() {
        return data != null && data.content != null ? data.content.string : null;
    }

    public Integer getMsgType() {
        return data != null ? data.msgType : null;
    }

    public Long getMsgId() {
        return data != null ? data.msgId : null;
    }

    public Long getCreateTime() {
        return data != null ? data.createTime : null;
    }

    public String getPushContent() {
        return data != null ? data.pushContent : null;
    }
}

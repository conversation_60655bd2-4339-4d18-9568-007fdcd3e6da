package com.tourism.chat.wx.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tourism.chat.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

/**
 * GeWe消息记录实体
 */
@TableName("gewe_message")
@Getter
@Setter
@ToString
public class    GeWeMessage extends BaseEntity {

    @Id
    @TableField(value = "id")
    private Long id;

    /**
     * 消息ID（来自GeWe API）
     */
    @TableField(value = "message_id")
    private String messageId;

    /**
     * 发送方微信ID
     */
    @TableField(value = "from_wx_id")
    private String fromWxId;

    /**
     * 接收方微信ID
     */
    @TableField(value = "to_wx_id")
    private String toWxId;

    /**
     * 消息类型：1-文本，2-图片，3-语音，4-视频，5-文件，6-位置，7-表情，8-链接，9-系统消息
     */
    @TableField(value = "message_type")
    private Integer messageType;


    /**
     * 图片url,视频url,文件url,链接url
     */
    private String extUrl;

    /**
     * 消息内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 是否为群聊消息
     */
    @TableField(value = "is_group")
    private Boolean isGroup = false;

    /**
     * 群聊中@的成员微信ID列表（JSON格式）
     */
    @TableField(value = "at_wx_ids")
    private String atWxIds;

    /**
     * 消息方向：1-发送，2-接收
     */
    @TableField(value = "direction")
    private Integer direction;

    /**
     * 消息状态：1-发送中，2-已发送，3-发送失败，4-已接收，5-已读
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 发送时间
     */
    @TableField(value = "send_time")
    private LocalDateTime sendTime;

    /**
     * 接收时间
     */
    @TableField(value = "receive_time")
    private LocalDateTime receiveTime;


    /**
     * 扩展信息（JSON格式）
     */
    @TableField(value = "extra_data")
    private String extraData;


}

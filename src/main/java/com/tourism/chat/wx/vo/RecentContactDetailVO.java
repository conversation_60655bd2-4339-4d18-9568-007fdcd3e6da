package com.tourism.chat.wx.vo;

import com.tourism.chat.wx.entity.GeWeFriend;
import com.tourism.chat.wx.entity.GeWeMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecentContactDetailVO {
    private GeWeFriend contact;
    private GeWeMessage lastMessage;
    private LocalDateTime lastMessageTime;
    private int unreadCount;
}


package com.tourism.chat.wx.controller;

import com.tourism.chat.common.response.R;
import com.tourism.chat.wx.entity.GeWeFriend;
import com.tourism.chat.wx.entity.GeWeMessage;
import com.tourism.chat.wx.mappper.GeWeFriendMapper;
import com.tourism.chat.wx.mappper.GeWeMessageMapper;
import com.tourism.chat.wx.vo.RecentContactDetailVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/gewe/friend/message")
@Tag(name = "消息最近联系人")
public class    RecentContactsController {

    @Resource
    private GeWeMessageMapper messageMapper;

    @Resource
    private GeWeFriendMapper friendMapper;

    @Operation(summary = "最近联系人详情：含未读数与最后一条消息")
    @GetMapping("/recent/contacts/detail")
    public R<List<RecentContactDetailVO>> recentContactsDetail(
            @RequestParam("wxId") String wxId,
            @RequestParam(value = "limit", required = false, defaultValue = "50") int limit
    ) {
        if (!StringUtils.hasText(wxId)) {
            return R.fail(400, "wxId 不能为空");
        }

        // 最近好友联系人
        List<String> friendIds = messageMapper.findRecentChatContacts(wxId, new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, limit));
        // 最近群聊
        List<String> groups = messageMapper.findRecentGroups(wxId, new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, limit));

        List<RecentContactDetailVO> list = new ArrayList<>();

        // 好友详情
        for (String cid : friendIds) {
            GeWeFriend f = friendMapper.findByWxId(cid);
            if (f == null) continue;
            GeWeMessage last = messageMapper.findLastFriendMessage(wxId, cid);
            int unread = messageMapper.countUnreadFriend(wxId, cid);
            RecentContactDetailVO vo = new RecentContactDetailVO(f, last, last != null ? last.getSendTime() : null, unread);
            list.add(vo);
        }

        // 群聊详情
        for (String gid : groups) {
            GeWeFriend g = friendMapper.findByWxId(gid);
            if (g == null) continue;
            GeWeMessage last = messageMapper.findLastGroupMessage(gid);
            int unread = messageMapper.countUnreadGroup(gid);
            RecentContactDetailVO vo = new RecentContactDetailVO(g, last, last != null ? last.getSendTime() : null, unread);
            list.add(vo);
        }

        // 简单按时间降序
        list.sort((a, b) -> {
            var ta = a.getLastMessageTime();
            var tb = b.getLastMessageTime();
            if (ta == null && tb == null) return 0;
            if (ta == null) return 1;
            if (tb == null) return -1;
            return tb.compareTo(ta);
        });

        return R.ok(list);
    }
}


package com.tourism.chat.wx.controller;

import com.tourism.chat.common.response.R;
import com.tourism.chat.wx.mappper.GeWeMessageMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/gewe/friend/message")
@Tag(name = "消息已读状态")
public class ReadStatusController {

    @Resource
    private GeWeMessageMapper messageMapper;

    @Operation(summary = "将好友会话标记为已读")
    @PostMapping("/read/conversation")
    public R<Integer> markFriendConversationRead(@RequestParam("wxId") String wxId,
                                                 @RequestParam("contactWxId") String contactWxId) {
        if (!StringUtils.hasText(wxId) || !StringUtils.hasText(contactWxId)) {
            return R.fail(400, "参数不能为空");
        }
        int updated = messageMapper.markFriendConversationRead(wxId, contactWxId);
        return R.ok(updated);
    }

    @Operation(summary = "将群聊标记为已读")
    @PostMapping("/read/group")
    public R<Integer> markGroupRead(@RequestParam("groupWxId") String groupWxId) {
        if (!StringUtils.hasText(groupWxId)) {
            return R.fail(400, "groupWxId 不能为空");
        }
        int updated = messageMapper.markGroupConversationRead(groupWxId);
        return R.ok(updated);
    }
}


package com.tourism.chat.auth;

import com.tourism.chat.common.response.R;
import com.tourism.chat.entity.User;
import com.tourism.chat.service.UserService;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.Data;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Authentication endpoints: login/logout.
 */
@RestController
@RequestMapping("/auth")
public class AuthController {

    private final JwtTokenProvider tokenProvider;
    private final UserService userService;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    public AuthController(JwtTokenProvider tokenProvider, UserService userService) {
        this.tokenProvider = tokenProvider;
        this.userService = userService;
    }

    @PostMapping("/login")
    public R<?> login(@Validated @RequestBody LoginRequest request) {
        User user = userService.findByNameAndTenantId(request.getPhone(), request.getTenantId());
       if (user == null) {
           return R.fail(401, "User not found or tenant mismatch");
       }
       // if (user.getPassword() != null && !user.getPassword().isEmpty()) {
           // if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
           //     return R.fail(401, "Invalid credentials");
           // }
       // }
        String token = tokenProvider.createToken(user);
        return R.success(new LoginResponse(token));
    }

    @PostMapping("/logout")
    public R<?> logout() {
        // Stateless JWT: client just discards token. For blacklisting, use Redis if needed.
        return R.success();
    }

    @Data
    public static class LoginRequest {
        private String phone;
        private String password;
        private Long tenantId;
    }

    @Data
    public static class LoginResponse {
        private final String token;
    }
}


package com.tourism.chat.auth;

import com.tourism.chat.entity.User;
import com.tourism.chat.service.UserService;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Optional Spring Security user service. Here we don't query by username only because we also need tenant.
 * Not used directly in this skeleton, but provided for extension.
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserService userService;

    public UserDetailsServiceImpl(UserService userService) {
        this.userService = userService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // Fallback: tenant-less lookup (NOT recommended). Replace with proper logic if you use it.
        User user = userService.findByNameAndTenantId(username, -1L);
        if (user == null) {
            throw new UsernameNotFoundException("User not found");
        }
        return new org.springframework.security.core.userdetails.User(
                user.getName(), user.getPassword(), List.of(new SimpleGrantedAuthority("ROLE_USER"))
        );
    }
}


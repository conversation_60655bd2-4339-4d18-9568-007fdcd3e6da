package com.tourism.chat.aspect;

import com.tourism.chat.tenant.TenantContextHolder;
import com.tourism.chat.tenant.UserContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class DisableTenantAspect {

    @Around("@annotation(com.tourism.chat.aspect.IgnoreTenant)")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        UserContext context = TenantContextHolder.getContext();
        Boolean prevIgnoreTenant = context.getIgnoreTenant();
        try {
            context.setIgnoreTenant(true);
            return proceedingJoinPoint.proceed();
        } finally {
            context.setIgnoreTenant(prevIgnoreTenant);
        }
    }
}

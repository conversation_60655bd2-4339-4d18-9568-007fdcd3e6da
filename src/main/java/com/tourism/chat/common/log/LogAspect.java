package com.tourism.chat.common.log;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * Simple AOP logging around controller calls.
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void restController() {}

    @Around("restController()")
    public Object logAround(ProceedingJoinPoint pjp) throws Throwable {
        String signature = pjp.getSignature().toShortString();
        Object[] args = pjp.getArgs();
        log.info("[AOP] -> {} args={}", signature, Arrays.toString(args));
        long start = System.currentTimeMillis();
        try {
            Object result = pjp.proceed();
            long cost = System.currentTimeMillis() - start;
            log.info("[AOP] <- {} cost={}ms result={}", signature, cost, summarize(result));
            return result;
        } catch (Throwable t) {
            long cost = System.currentTimeMillis() - start;
            log.error("[AOP] !! {} cost={}ms error={}", signature, cost, t.toString());
            throw t;
        }
    }

    private String summarize(Object obj) {
        if (obj == null) return "null";
        String s = obj.toString();
        if (s.length() > 300) return s.substring(0, 300) + "...";
        return s;
    }
}


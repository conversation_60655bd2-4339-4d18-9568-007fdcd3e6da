package com.tourism.chat.group.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tourism.chat.common.response.R;
import com.tourism.chat.group.entity.GeweGroup;
import com.tourism.chat.group.entity.GeweGroupMember;
import com.tourism.chat.group.entity.GeweGroupMessage;
import com.tourism.chat.group.mapper.GeweGroupMapper;
import com.tourism.chat.group.mapper.GeweGroupMemberMapper;
import com.tourism.chat.group.mapper.GeweGroupMessageMapper;
import com.tourism.chat.wechat.mapper.GeweWechatAccountMapper;
import com.tourism.chat.wx.model.GeWeChatroomModels;
import com.tourism.chat.wx.service.GeWeApiService;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Group chat API proxy and persistence.
 */
@RestController
@RequestMapping("/gewe/group")
@Slf4j
public class GeWeGroupController {

    @Resource
    private GeWeApiService geWeApiService;
    @Resource
    private GeweGroupMapper groupMapper;
    @Resource
    private GeweGroupMemberMapper memberMapper;

    @Resource
    private GeweGroupMessageMapper groupMessageMapper;
    @Autowired
    private GeweWechatAccountMapper geweWechatAccountMapper;

    @PostMapping("/getChatroomInfo")
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> getChatroomInfo(@RequestBody GetInfoReq req) {
        if (req == null || !StringUtils.hasText(req.getChatroomId())) {
            return R.fail(400, "chatroomId is required");
        }
        GeWeChatroomModels.ChatroomInfoData data = geWeApiService.getChatroomInfo(req.getChatroomId());
        if (data == null)
            return R.fail(500, "3rd api failed");

        // upsert group
        GeweGroup group = groupMapper.selectOne(new LambdaQueryWrapper<GeweGroup>()
                .eq(GeweGroup::getChatroomId, data.getChatroomId()));
        LocalDateTime now = LocalDateTime.now();
        if (group == null) {
            group = new GeweGroup();
            group.setChatroomId(data.getChatroomId());
            group.setCreateTime(now);
        }
        group.setNickName(data.getNickName());
        group.setOwnerWxid(data.getChatRoomOwner());
        group.setSmallHeadImgUrl(data.getSmallHeadImgUrl());
        group.setRemark(data.getRemark());
        group.setUpdateTime(now);
        if (group.getId() == null) {
            groupMapper.insert(group);
        } else {
            groupMapper.updateById(group);
        }

        // refresh members: simple strategy - delete then insert
        memberMapper.delete(new LambdaQueryWrapper<GeweGroupMember>()
                .eq(GeweGroupMember::getChatroomId, data.getChatroomId()));
        if (data.getMemberList() != null) {
            for (GeWeChatroomModels.Member m : data.getMemberList()) {
                GeweGroupMember gm = new GeweGroupMember();
                gm.setChatroomId(data.getChatroomId());
                gm.setWxid(m.getWxid());
                gm.setNickName(m.getNickName());
                gm.setDisplayName(m.getDisplayName());
                gm.setInviterUserName(m.getInviterUserName());
                gm.setMemberFlag(m.getMemberFlag());
                gm.setSmallHeadImgUrl(m.getSmallHeadImgUrl());
                gm.setCreateTime(now);
                memberMapper.insert(gm);
            }
        }

        return R.success(Map.of(
                "chatroomId", group.getChatroomId(),
                "nickName", group.getNickName(),
                "ownerWxid", group.getOwnerWxid(),
                "smallHeadImgUrl", group.getSmallHeadImgUrl(),
                "memberCount", data.getMemberList() == null ? 0 : data.getMemberList().size()));
    }

    @PostMapping("/getChatroomMemberList")
    @Transactional(rollbackFor = Exception.class)
    public R<GeWeChatroomModels.MemberListData> getChatroomMemberList(@RequestBody GetInfoReq req) {
        if (req == null || !StringUtils.hasText(req.getChatroomId())) {
            return R.fail(400, "chatroomId is required");
        }
        GeWeChatroomModels.MemberListData data = new GeWeChatroomModels.MemberListData();
         String chatroomOwner = null;
         List<String> adminWxid = null;
        List<GeweGroupMember> geweGroupMembers = memberMapper.selectList(new LambdaQueryWrapper<GeweGroupMember>()
                .eq(GeweGroupMember::getChatroomId, req.getChatroomId()));
        // geweGroupMembers to MemberListData
        data.setMemberList(geweGroupMembers.stream().map(gm -> {
                    GeWeChatroomModels.Member m = new GeWeChatroomModels.Member();
                    m.setWxid(gm.getWxid());
                    m.setNickName(gm.getNickName());
                    m.setDisplayName(gm.getDisplayName());
                    m.setInviterUserName(gm.getInviterUserName());
                    m.setMemberFlag(gm.getMemberFlag());
                    m.setSmallHeadImgUrl(gm.getSmallHeadImgUrl());
                    Integer memberFlag = gm.getMemberFlag();
                    // switch (memberFlag) {
                    //     case 0 -> m.setMemberFlagName("普通成员");
                    //     case 1 -> m.setMemberFlagName("群主");
                    //     case 2 -> m.setMemberFlagName("管理员");
                    //     default -> m.setMemberFlagName("未知");
                    // }
                    return m;
                }
        ).toList());
        // GeWeChatroomModels.MemberListData data = geWeApiService.getChatroomMemberList(req.getChatroomId());
        //   if (data == null)
        //       return R.fail(500, "3rd api failed");
/*
        // upsert members snapshot
        LocalDateTime now = LocalDateTime.now();
        memberMapper.delete(new LambdaQueryWrapper<GeweGroupMember>()
                .eq(GeweGroupMember::getChatroomId, req.getChatroomId()));
        if (geweGroupMembers != null) {
            for (GeWeChatroomModels.Member m : data.getMemberList()) {
                GeweGroupMember gm = new GeweGroupMember();
                gm.setChatroomId(req.getChatroomId());
                gm.setWxid(m.getWxid());
                gm.setNickName(m.getNickName());
                gm.setDisplayName(m.getDisplayName());
                gm.setInviterUserName(m.getInviterUserName());
                gm.setMemberFlag(m.getMemberFlag());
                gm.setSmallHeadImgUrl(m.getSmallHeadImgUrl());
                gm.setCreateTime(now);
                memberMapper.insert(gm);
            }
        }*/
        return R.success(data);
    }

    @PostMapping("/getChatroomMemberDetail")
    public R<List<GeWeChatroomModels.MemberDetail>> getChatroomMemberDetail(@RequestBody MemberDetailReq req) {
        if (req == null || !StringUtils.hasText(req.getChatroomId()) || req.getMemberWxids() == null
                || req.getMemberWxids().isEmpty()) {
            return R.fail(400, "chatroomId and memberWxids are required");
        }
        List<GeWeChatroomModels.MemberDetail> list = geWeApiService.getChatroomMemberDetail(
                req.getChatroomId(), req.getMemberWxids());
        return R.success(list);
    }

    // ========== New endpoints ==========
    // 获取群列表（全部或按owner/成员过滤，当前先返回全部）
    @GetMapping("/list")
    public R<List<Map<String, Object>>> listGroups() {
        List<GeweGroup> groups = groupMapper.selectList(null);
        List<Map<String, Object>> list = groups.stream().map(g -> {
            Map<String, Object> m = new LinkedHashMap<>();
            m.put("chatroomId", g.getChatroomId());
            m.put("nickName", g.getNickName());
            m.put("ownerWxid", g.getOwnerWxid());
            m.put("smallHeadImgUrl", g.getSmallHeadImgUrl());
            m.put("remark", g.getRemark());
            return m;
        }).toList();
        return R.success(list);
    }

    // 根据 wxId 获取该账号所在的群（群主或成员）
    @GetMapping("/listByWxId")
    public R<List<Map<String, Object>>> listByWxId(@RequestParam String wxId) {
        // 群主为该 wxId
        List<GeweGroup> owned = groupMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GeweGroup>()
                        .eq(GeweGroup::getMasterWxid, wxId));
        // 作为成员加入的群
        List<GeweGroupMember> memberships = memberMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GeweGroupMember>()
                        .eq(GeweGroupMember::getWxid, wxId));
        java.util.Set<String> chatroomIds = new java.util.LinkedHashSet<>();
        owned.forEach(g -> chatroomIds.add(g.getChatroomId()));
        memberships.forEach(m -> chatroomIds.add(m.getChatroomId()));

        List<GeweGroup> groups;
        if (chatroomIds.isEmpty()) {
            groups = java.util.Collections.emptyList();
        } else {
            groups = groupMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GeweGroup>()
                            .in(GeweGroup::getChatroomId, chatroomIds));
        }

        List<Map<String, Object>> list = groups.stream().map(g -> {
            Map<String, Object> m = new java.util.LinkedHashMap<>();
            m.put("chatroomId", g.getChatroomId());
            m.put("nickName", g.getNickName());
            m.put("ownerWxid", g.getOwnerWxid());
            m.put("smallHeadImgUrl", g.getSmallHeadImgUrl());
            m.put("remark", g.getRemark());
            return m;
        }).toList();
        return R.success(list);
    }

    // 获取群聊历史消息（从 gewe_group_message 表）
    @GetMapping("/message/history")
    public R<Map<String, Object>> getGroupMessages(
            @RequestParam String chatroomId,
            @RequestParam(defaultValue = "0") long page,
            @RequestParam(defaultValue = "20") long size) {
        // 这里不使用 MyBatis-Plus Page 泛型，简单手写分页
        long offset = page * size;
        // 实际查询：按 create_time 倒序
        List<GeweGroupMessage> messages = groupMessageMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GeweGroupMessage>()
                        .eq(GeweGroupMessage::getChatroomId, chatroomId)
                        .orderByDesc(GeweGroupMessage::getCreateTime));
        int from = (int) Math.min(offset, messages.size());
        int to = (int) Math.min(offset + size, messages.size());
        List<GeweGroupMessage> pageList = messages.subList(from, to);

        // 适配前端 GeWeMessage 结构字段
        List<Map<String, Object>> records = pageList.stream().map(msg -> {
            Map<String, Object> m = new LinkedHashMap<>();
            m.put("id", msg.getId());
            m.put("messageId", null);
            m.put("fromWxId", msg.getSenderWxid());
            m.put("toWxId", msg.getChatroomId());
            m.put("content", msg.getContent());
            m.put("messageType", msg.getMessageType());
            m.put("createTime", msg.getCreateTime());
            m.put("isGroup", true);
            m.put("direction", 0); // 接收视为0，若有 from=当前用户可置1
            m.put("atWxIds", msg.getAtWxid());
            return m;
        }).toList();

        Map<String, Object> pageResp = new LinkedHashMap<>();
        pageResp.put("records", records);
        pageResp.put("total", messages.size());
        pageResp.put("size", size);
        pageResp.put("current", page);
        pageResp.put("pages", (messages.size() + size - 1) / size);
        pageResp.put("hasNext", to < messages.size());
        return R.success(pageResp);
    }

    @Data
    public static class GetInfoReq {
        private String chatroomId;
    }

    @Data
    public static class MemberDetailReq {
        private String chatroomId;
        private List<String> memberWxids;
    }
}

package com.tourism.chat.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.tourism.chat.entity.BaseEntity;
import lombok.Data;

@Data
@TableName("gewe_group")
public class GeweGroup extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    // 归属微信
    private String masterWxid;

    @TableField("owner_wxid")
    private String ownerWxid;

    @TableField("chatroom_id")
    private String chatroomId;

    @TableField("nick_name")
    private String nickName;

    @TableField("small_head_img_url")
    private String smallHeadImgUrl;

    @TableField("remark")
    private String remark;
}


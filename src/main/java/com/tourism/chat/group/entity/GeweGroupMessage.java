package com.tourism.chat.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.tourism.chat.entity.BaseEntity;
import lombok.Data;

@Data
@TableName("gewe_group_message")
public class GeweGroupMessage extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("chatroom_id")
    private String chatroomId;

    @TableField("sender_wxid")
    private String senderWxid;

    @TableField("content")
    private String content;

    @TableField("at_wxid")
    private String atWxid; // JSON 字符串或逗号分隔

    @TableField("message_type")
    private String messageType; // 文本、图片、视频等（可与既有整型映射）
}


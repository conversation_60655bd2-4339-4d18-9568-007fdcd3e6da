package com.tourism.chat.tenant;

import io.jsonwebtoken.lang.Assert;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;

import java.util.function.Function;

/**
 * Holds current tenant id in ThreadLocal for multi-tenant filtering.
 */
@Getter
public class TenantContextHolder {
    private static final ThreadLocal<UserContext> TENANT_HOLDER = new ThreadLocal<>();

    public static final String TENANT_HEADER = "X-Tenant-ID";

    public static final String TENANT_ID = "tenantId";
    // name
    public static final String TENANT_NAME = "tenantName";


    @Value("${tenant.enabled:true}")
    private static Boolean tenantEnabled;

    public static void setUserContext(UserContext context) {
        TENANT_HOLDER.set(context);
    }

    public static Long getTenantId() {
        UserContext userContext = TENANT_HOLDER.get();
        if (userContext == null) return null;
        return userContext.getTenantId();
    }

    public static void clear() {
        TENANT_HOLDER.remove();
    }

    public static void setTenantId(long tenantId) {
        TENANT_HOLDER.set(new UserContext(tenantId));
    }
    public static UserContext  getContext() {
        UserContext ctx = TENANT_HOLDER.get();
        if (ctx == null) {
            ctx = new UserContext(null, null);
            setContext(ctx);
        }
        return ctx;
    }

    public static void setContext(UserContext context) {
        Assert.notNull(context, "Only non-null UserContext instances are permitted");
        if (context.getIgnoreTenant() == null) {
            context.setIgnoreTenant(tenantEnabled != null && !tenantEnabled);
            /*if (log.isDebugEnabled()) {
                log.debug("set ig tenant:{}",context.getIgnoreTenant());
            }*/
        }
        TENANT_HOLDER.set(context);
    }
    public static <T, R> R ignoreTenant(Function<T, R> callback) {
        return useContext(TENANT_HOLDER.get(), true, callback, null);
    }
    public static <T, R> R useContext(UserContext tempContext, Boolean ignoreTenant, Function<T, R> callback, T arg) {
        Boolean prevIgnoreTenant = tempContext.getIgnoreTenant();
        tempContext.setIgnoreTenant(ignoreTenant);
        UserContext prevContext = getContext();
        try {
            setContext(tempContext);
            return callback.apply(arg);
        } finally {
            setContext(prevContext);
            tempContext.setIgnoreTenant(prevIgnoreTenant);
        }
    }
}


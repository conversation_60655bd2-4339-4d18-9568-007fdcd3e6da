package com.tourism.chat.tenant;

import lombok.Data;

@Data
public class UserContext {
    private Long userId;
    private String userName;
    private Long tenantId;
    private String tenantName;
    private String phone;

    private Boolean ignoreTenant = false;
    public UserContext(long tenantId) {
        this.tenantId = tenantId;
    }


    public UserContext(Long userId, Long tenantId) {
        this.userId = userId;
        this.userName = userName;
        this.tenantId = tenantId;
        this.tenantName = tenantName;
        this.phone = phone;
    }

    public UserContext(Long userId, Long tenantId,String phone) {
        this.userId = userId;
        this.tenantId = tenantId;
        this.phone = phone;
    }
}

package com.tourism.chat.ws;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

/**
 * 简单的文本 WebSocket 处理器，按 wxId 绑定连接。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WxWebSocketHandler extends TextWebSocketHandler {

    private final WebSocketSessionRegistry registry;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Object keyObj = session.getAttributes().get("sessionKey");
        String sessionKey = keyObj == null ? null : keyObj.toString();
        if (sessionKey == null || sessionKey.isBlank()) {
            log.warn("WebSocket 连接无 sessionKey，关闭");
            session.close(CloseStatus.BAD_DATA);
            return;
        }
        registry.add(sessionKey, session);
        log.info("WebSocket 已建立，sessionKey={}", sessionKey);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) {
        // 当前无需处理客户端消息，可按需扩展 ping/pong 或心跳
        log.info("收到客户端消息: {}", message.getPayload());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        Object keyObj = session.getAttributes().get("sessionKey");
        String sessionKey = keyObj == null ? null : keyObj.toString();
        if (sessionKey != null) {
            registry.remove(sessionKey);
            log.info("WebSocket 已关闭，sessionKey={}，status={}", sessionKey, status);
        }
    }
}

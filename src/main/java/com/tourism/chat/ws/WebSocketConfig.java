package com.tourism.chat.ws;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
@RequiredArgsConstructor
public class WebSocketConfig implements WebSocketConfigurer {

    private final WxWebSocketHandler wxWebSocketHandler;
    private final WxIdHandshakeInterceptor wxIdHandshakeInterceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(wxWebSocketHandler, "/ws")
                .addInterceptors(wxIdHandshakeInterceptor)
                .setAllowedOriginPatterns("*");
    }
}


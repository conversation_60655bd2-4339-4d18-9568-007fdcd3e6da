package com.tourism.chat.config;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.tourism.chat.tenant.DefaultMetaObjectHandler;
import com.tourism.chat.tenant.MyTenantHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Plus configuration with multi-tenant support.
 */
@Configuration
@MapperScan("com.tourism.chat.mapper")
public class MyBatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor(MyTenantHandler myTenantHandler) {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(myTenantHandler));
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }

    // @Bean
    // public MySqlInjector sqlInjector() {
    //     return new MySqlInjector();
    // }

    // @Bean
    // public ConfigurationCustomizer configurationCustomizer() {
    //     return configuration -> configuration.setDefaultExecutorType(false);
    // }

    @Bean
    public DefaultMetaObjectHandler defaultMetaObjectHandler() {
        return new DefaultMetaObjectHandler();
    }
}


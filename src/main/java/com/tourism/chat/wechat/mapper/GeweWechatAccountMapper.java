package com.tourism.chat.wechat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.chat.wechat.dto.WechatAccountDTO;
import com.tourism.chat.wechat.entity.GeweWechatAcount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface GeweWechatAccountMapper extends BaseMapper<GeweWechatAcount> {

    @Select("\n" +
            "SELECT w.id, w.tenant_id AS tenantId, w.wx_id AS wxId,\n" +
            "       w.big_head_img_url AS bigHeadImgUrl, w.small_head_img_url AS smallHeadImgUrl,\n" +
            "       (w.online = 1) AS online\n" +
            "  FROM gewe_wechat w\n" +
            "  JOIN user_wechat_relation r ON r.gewe_wechat_id = w.id AND r.is_deleted = 0\n" +
            " WHERE r.user_id = #{userId}\n" +
            "   AND w.is_deleted = 0\n" +
            " ORDER BY w.update_time DESC, w.id DESC")
    List<WechatAccountDTO> findAccountsByUser(@Param("userId") Long userId);
}


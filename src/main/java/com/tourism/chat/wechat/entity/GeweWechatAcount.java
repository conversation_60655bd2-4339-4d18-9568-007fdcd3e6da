package com.tourism.chat.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tourism.chat.entity.BaseEntity;
import lombok.Data;

/**
 * Entity for table: gewe_wechat
 */
@Data
@TableName("gewe_wechat")
public class GeweWechatAcount extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    // BaseEntity.tenantId -> maps to column tenant_id (via underscore naming)

    @TableField("token_id")
    private String tokenId;

    @TableField("app_id")
    private String appId;

    @TableField("wx_id")
    private String wxId;

    @TableField("big_head_img_url")
    private String bigHeadImgUrl;

    @TableField("small_head_img_url")
    private String smallHeadImgUrl;

    // tinyint(1) -> Bo<PERSON>an
    @TableField("online")
    private Boolean online;

    // create_by, create_time, update_by, update_time, is_deleted are inherited from BaseEntity
}


package com.tourism.chat.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.chat.wechat.dto.WechatAccountDTO;
import com.tourism.chat.wechat.entity.GeweWechatAcount;

import java.util.List;

public interface GeweWechatAcountService extends IService<GeweWechatAcount> {
    List<WechatAccountDTO> findAccountsByUser(Long userId);

    GeweWechatAcount queryAccountByWxId(String wxId);
}


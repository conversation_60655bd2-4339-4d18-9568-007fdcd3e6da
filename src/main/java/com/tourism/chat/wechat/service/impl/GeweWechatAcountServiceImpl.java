package com.tourism.chat.wechat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.chat.aspect.IgnoreTenant;
import com.tourism.chat.wechat.dto.WechatAccountDTO;
import com.tourism.chat.wechat.entity.GeweWechatAcount;
import com.tourism.chat.wechat.mapper.GeweWechatAccountMapper;
import com.tourism.chat.wechat.service.GeweWechatAcountService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GeweWechatAcountServiceImpl extends ServiceImpl<GeweWechatAccountMapper, GeweWechatAcount> implements GeweWechatAcountService {
    @Override
    @IgnoreTenant
    public List<WechatAccountDTO> findAccountsByUser(Long userId) {
        return baseMapper.findAccountsByUser(userId);
    }

    @Override
    @IgnoreTenant
    public GeweWechatAcount queryAccountByWxId(String wxId) {
        return this.lambdaQuery().eq(GeweWechatAcount::getWxId, wxId).one();
    }
}


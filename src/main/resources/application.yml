server:
  port: 18080
#context path
  servlet:
    context-path: /api
spring:
  datasource:
#    url: jdbc:mysql://*************:3306/tourism?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    url: jdbc:mysql://*************:3306/tourism?useSSL=false&serverTimezone=Asia/Shanghai&characterEncoding=utf8
    username: tourism
    password: CeLxp7bF7yTDnYw6
    driver-class-name: com.mysql.cj.jdbc.Driver
  data:
    redis:
      host: 127.0.0.1
#      password: foot123
      password:
      database: 0
      port: 6379

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

jwt:
  secret: demoSecretKey123456
  expiration: 10080

gewe:
  api:
    baseUrl: http://api.geweapi.com
    appId: wx_Vlj-0pB6Z79HRw6V2YKhR
    token: 9d454e71-9b59-4db8-a5d0-74c7ecaed862

